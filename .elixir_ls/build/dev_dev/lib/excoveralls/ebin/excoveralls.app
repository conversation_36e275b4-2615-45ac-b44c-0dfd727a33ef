{application,excoveralls,
             [{modules,['Elixir.ExCoveralls','Elixir.ExCoveralls.Circle',
                        'Elixir.ExCoveralls.Cobertura',
                        'Elixir.ExCoveralls.ConfServer',
                        'Elixir.ExCoveralls.Cover','Elixir.ExCoveralls.Drone',
                        'Elixir.ExCoveralls.Github',
                        'Elixir.ExCoveralls.Gitlab','Elixir.ExCoveralls.Html',
                        'Elixir.ExCoveralls.Html.Safe',
                        'Elixir.ExCoveralls.Html.View',
                        'Elixir.ExCoveralls.Html.View.PathHelper',
                        'Elixir.ExCoveralls.Ignore',
                        'Elixir.ExCoveralls.Ignore.State',
                        'Elixir.ExCoveralls.InvalidConfigError',
                        'Elixir.ExCoveralls.InvalidOptionError',
                        'Elixir.ExCoveralls.Json','Elixir.ExCoveralls.Lcov',
                        'Elixir.ExCoveralls.Local',
                        'Elixir.ExCoveralls.Local.Count',
                        'Elixir.ExCoveralls.PathReader',
                        'Elixir.ExCoveralls.Post','Elixir.ExCoveralls.Poster',
                        'Elixir.ExCoveralls.ReportUploadError',
                        'Elixir.ExCoveralls.Semaphore',
                        'Elixir.ExCoveralls.Settings',
                        'Elixir.ExCoveralls.Settings.Files',
                        'Elixir.ExCoveralls.StatServer',
                        'Elixir.ExCoveralls.Stats',
                        'Elixir.ExCoveralls.Stats.Line',
                        'Elixir.ExCoveralls.Stats.Source',
                        'Elixir.ExCoveralls.StopWords',
                        'Elixir.ExCoveralls.SubApps',
                        'Elixir.ExCoveralls.Task.Util',
                        'Elixir.ExCoveralls.Travis','Elixir.ExCoveralls.Xml',
                        'Elixir.Mix.Tasks.Coveralls',
                        'Elixir.Mix.Tasks.Coveralls.Circle',
                        'Elixir.Mix.Tasks.Coveralls.Cobertura',
                        'Elixir.Mix.Tasks.Coveralls.Detail',
                        'Elixir.Mix.Tasks.Coveralls.Drone',
                        'Elixir.Mix.Tasks.Coveralls.Github',
                        'Elixir.Mix.Tasks.Coveralls.Gitlab',
                        'Elixir.Mix.Tasks.Coveralls.Html',
                        'Elixir.Mix.Tasks.Coveralls.Json',
                        'Elixir.Mix.Tasks.Coveralls.Lcov',
                        'Elixir.Mix.Tasks.Coveralls.Multiple',
                        'Elixir.Mix.Tasks.Coveralls.Post',
                        'Elixir.Mix.Tasks.Coveralls.Runner',
                        'Elixir.Mix.Tasks.Coveralls.Semaphore',
                        'Elixir.Mix.Tasks.Coveralls.Travis',
                        'Elixir.Mix.Tasks.Coveralls.Xml']},
              {optional_applications,[castore]},
              {applications,[kernel,stdlib,elixir,eex,tools,xmerl,inets,ssl,
                             public_key,castore,jason]},
              {description,"Coverage report tool for Elixir with coveralls.io integration.\n"},
              {registered,[]},
              {vsn,"0.18.5"}]}.
