defmodule Drops.Relation.Plugins.Pagination do
  @moduledoc """
  Plugin that provides pagination functionality for relation modules.

  This plugin adds pagination functions that return `Drops.Relation.Loaded` structs
  containing both the paginated data and metadata for navigation.

  ## Functions Added

  - `page/1` - Load a specific page with default per_page
  - `page/2` - Load a specific page with custom per_page
  - `per_page/1` - Set per_page for subsequent pagination
  - `per_page/2` - Set per_page on an existing relation

  ## Usage

      defmodule MyApp.Users do
        use Drops.Relation, repo: MyApp.Repo

        schema("users", infer: true)
      end

      # Basic pagination
      page1 = Users.page(1)                    # First page with default per_page (20)
      page2 = Users.page(2, 10)                # Second page with 10 records per page

      # Set per_page first, then paginate
      small_pages = Users.per_page(5)
      page1_small = Users.page(small_pages, 1) # First page with 5 records

      # Chain with other operations
      active_page1 = Users
                     |> Users.restrict(active: true)
                     |> Users.order(:name)
                     |> Users.per_page(10)
                     |> Users.page(1)

  ## Working with Results

      loaded = Users.page(1)

      # Access data using Enumerable protocol
      users = Enum.to_list(loaded)
      first_user = Enum.at(loaded, 0)
      user_names = Enum.map(loaded, & &1.name)

      # Access pagination metadata
      loaded.page         # => 1
      loaded.per_page     # => 20
      loaded.total_count  # => 150
      loaded.total_pages  # => 8
      loaded.has_next     # => true
      loaded.has_prev     # => false

  ## Navigation

      # Generate pagination links
      if loaded.has_prev do
        link("Previous", to: "/users?page=\#{loaded.page - 1}")
      end

      for page_num <- Drops.Relation.Loaded.page_range(loaded) do
        if page_num == loaded.page do
          content_tag(:span, page_num, class: "current")
        else
          link(page_num, to: "/users?page=\#{page_num}")
        end
      end

      if loaded.has_next do
        link("Next", to: "/users?page=\#{loaded.page + 1}")
      end

  ## Configuration

  The default per_page value can be configured:

      config :my_app, :drops,
        relation: [
          default_per_page: 25
        ]

  If not configured, the default per_page is 20.
  """

  alias Drops.Relation.Loaded
  alias Drops.Relation.Plugins.Reading

  use Drops.Relation.Plugin

  @default_per_page 20

  def on(:before_compile, _relation, _) do
    quote do
      alias unquote(__MODULE__)

      delegate_to(page(page_number), to: Pagination)
      delegate_to(page(page_number, per_page), to: Pagination)
      delegate_to(page(relation, page_number), to: Pagination)
      delegate_to(per_page(per_page_value), to: Pagination)
      delegate_to(per_page(relation, per_page_value), to: Pagination)

      defquery paginate(offset, per_page) do
        from(q in relation(), offset: ^offset, limit: ^per_page)
      end
    end
  end

  @doc """
  Loads a specific page with the default per_page setting.

  ## Parameters

  - `page_number` - The page number to load (1-based)
  - `opts` - Additional options (automatically provided by delegate_to)

  ## Returns

  Returns a `Drops.Relation.Loaded` struct with the paginated data and metadata.

  ## Examples

      # Load first page with default per_page
      page1 = Users.page(1)

      # Load third page
      page3 = Users.page(3)
  """
  @spec page(pos_integer(), keyword()) :: Loaded.t()
  def page(page_number, opts) when is_integer(page_number) and page_number > 0 do
    per_page_value = get_default_per_page(opts[:relation])
    page(page_number, per_page_value, opts)
  end

  @doc """
  Loads a specific page with a custom per_page setting.

  ## Parameters

  - `page_number` - The page number to load (1-based)
  - `per_page_value` - Number of records per page
  - `opts` - Additional options (automatically provided by delegate_to)

  ## Returns

  Returns a `Drops.Relation.Loaded` struct with the paginated data and metadata.

  ## Examples

      # Load first page with 10 records per page
      page1 = Users.page(1, 10)

      # Load second page with 5 records per page
      page2 = Users.page(2, 5)
  """
  @spec page(pos_integer(), pos_integer(), keyword()) :: Loaded.t()
  def page(page_number, per_page_value, opts)
      when is_integer(page_number) and page_number > 0 and is_integer(per_page_value) and
             per_page_value > 0 do
    {relation, _repo, queryable, _rest_opts} = Reading.clean_opts(opts)

    total_count = count_records(relation, queryable, opts)
    offset = (page_number - 1) * per_page_value
    total_pages = calculate_total_pages(total_count, per_page_value)

    pagination_meta = %{
      page: page_number,
      per_page: per_page_value,
      total_count: total_count,
      total_pages: total_pages,
      has_next: page_number < total_pages,
      has_prev: page_number > 1,
      offset: offset
    }

    relation.load(
      relation.paginate(page_number, per_page_value),
      %{pagination: pagination_meta}
    )
  end

  @spec page(struct(), pos_integer(), keyword()) :: Loaded.t()
  def page(%{__struct__: relation_module} = relation, page_number, opts)
      when is_integer(page_number) and page_number > 0 do
    per_page_value = get_per_page_from_relation(relation) || get_default_per_page(relation_module)
    total_count = count_records(relation_module, relation, opts)
    offset = (page_number - 1) * per_page_value
    total_pages = calculate_total_pages(total_count, per_page_value)

    pagination_meta = %{
      page: page_number,
      per_page: per_page_value,
      total_count: total_count,
      total_pages: total_pages,
      has_next: page_number < total_pages,
      has_prev: page_number > 1,
      offset: offset
    }

    relation_module.load(relation_module.paginate(relation, page_number, per_page_value), %{
      pagination: pagination_meta
    })
  end

  @doc """
  Sets the per_page value for subsequent pagination operations.

  ## Parameters

  - `per_page_value` - Number of records per page
  - `opts` - Additional options (automatically provided by delegate_to)

  ## Returns

  Returns a relation struct with per_page metadata.

  ## Examples

      # Set per_page for later use
      relation = Users.per_page(10)
      page1 = Users.page(relation, 1)
  """
  @spec per_page(pos_integer(), keyword()) :: struct()
  def per_page(per_page_value, opts) when is_integer(per_page_value) and per_page_value > 0 do
    relation = opts[:relation]
    queryable = Keyword.get(opts, :queryable, relation.queryable())

    # Create a new relation struct with pagination metadata
    updated_relation =
      case queryable do
        %{__struct__: ^relation} = relation_struct ->
          relation.add_operation(relation_struct, :pagination, per_page: per_page_value)

        _ ->
          # If queryable is not a relation struct, create a new one
          base_relation =
            struct(relation, queryable: queryable, operations: [], opts: [], meta: %{})

          relation.add_operation(base_relation, :pagination, per_page: per_page_value)
      end

    # Store pagination metadata in the meta field
    add_pagination_meta(updated_relation, %{per_page: per_page_value})
  end

  @doc """
  Sets the per_page value on an existing relation.

  ## Parameters

  - `relation` - An existing relation struct
  - `per_page_value` - Number of records per page
  - `opts` - Additional options (automatically provided by delegate_to)

  ## Returns

  Returns a relation struct with per_page metadata.

  ## Examples

      # Add per_page to existing query
      base_query = Users.restrict(active: true)
      paginated_query = Users.per_page(base_query, 15)
      page1 = Users.page(paginated_query, 1)
  """
  @spec per_page(struct(), pos_integer(), keyword()) :: struct()
  def per_page(%{__struct__: relation_module} = relation, per_page_value, _opts)
      when is_integer(per_page_value) and per_page_value > 0 do
    # Store pagination metadata in the meta field
    updated_relation =
      relation_module.add_operation(relation, :pagination, per_page: per_page_value)

    add_pagination_meta(updated_relation, %{per_page: per_page_value})
  end

  # Private helper functions

  defp get_default_per_page(relation) do
    case Drops.Relation.Plugin.config([relation: relation], :default_per_page) do
      nil -> @default_per_page
      value when is_integer(value) and value > 0 -> value
      _ -> @default_per_page
    end
  end

  defp get_per_page_from_relation(%{opts: opts}) do
    case Keyword.get(opts, :pagination) do
      pagination_opts when is_list(pagination_opts) ->
        Keyword.get(pagination_opts, :per_page)

      _ ->
        nil
    end
  end

  defp get_per_page_from_relation(_), do: nil

  defp count_records(relation, queryable, opts) do
    repo_opts = Keyword.delete(opts, :relation)
    repo = relation.opts(:repo)

    import Ecto.Query
    count_query = from(q in queryable, select: count())
    repo.one(count_query, repo_opts)
  end

  defp calculate_total_pages(total_count, per_page) when total_count >= 0 and per_page > 0 do
    case total_count do
      0 -> 0
      _ -> ceil(total_count / per_page)
    end
  end

  defp add_pagination_meta(%{meta: meta} = relation, pagination_data) do
    updated_meta = Map.put(meta, :pagination, pagination_data)
    %{relation | meta: updated_meta}
  end
end
